#!/bin/bash

# Cloudflare IP 优选工具 Docker 构建脚本

set -e

echo "========================================================"
echo "    Cloudflare IP 优选工具 Docker 镜像构建脚本"
echo "========================================================"
echo

# 检查 Docker 是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

echo "✅ Docker 已安装"

# 检查 docker-compose 是否安装
if command -v docker-compose &> /dev/null; then
    echo "✅ Docker Compose 已安装"
    COMPOSE_CMD="docker-compose"
elif docker compose version &> /dev/null; then
    echo "✅ Docker Compose (plugin) 已安装"
    COMPOSE_CMD="docker compose"
else
    echo "⚠️  Docker Compose 未安装，将只使用 Docker 命令"
    COMPOSE_CMD=""
fi

echo

# 构建镜像
echo "🔨 开始构建 Docker 镜像..."
docker build -t cfiptest:latest .

if [ $? -eq 0 ]; then
    echo "✅ Docker 镜像构建成功！"
else
    echo "❌ Docker 镜像构建失败"
    exit 1
fi

echo

# 显示镜像信息
echo "📋 镜像信息："
docker images cfiptest:latest

echo
echo "========================================================"
echo "                   构建完成！"
echo "========================================================"
echo
echo "🚀 使用方法："
echo
echo "1. 交互式模式："
echo "   docker run -it --rm --network host -v \$(pwd)/result:/app/result cfiptest:latest"
echo
echo "2. 更新 IP 列表："
echo "   docker run --rm --network host -v \$(pwd)/result:/app/result cfiptest:latest update"
echo
echo "3. 测试常用地区："
echo "   docker run --rm --network host -v \$(pwd)/result:/app/result cfiptest:latest common"
echo
echo "4. 自动测试所有地区："
echo "   docker run --rm --network host -v \$(pwd)/result:/app/result cfiptest:latest auto"
echo

if [ -n "$COMPOSE_CMD" ]; then
    echo "5. 使用 Docker Compose："
    echo "   $COMPOSE_CMD run --rm cfiptest"
    echo
    echo "6. 启动定时任务："
    echo "   $COMPOSE_CMD --profile cron up -d cfiptest-cron"
    echo
fi

echo "📖 详细使用说明请查看 Docker-README.md"
echo
