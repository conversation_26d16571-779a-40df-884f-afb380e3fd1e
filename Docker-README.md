# Cloudflare IP 优选工具 Docker 版本

这个 Docker 镜像封装了 Cloudflare IP 优选测试工具，支持 IPv4/IPv6 测试、地区选择、参数配置等功能。

## 🚀 快速开始

### 1. 构建 Docker 镜像

```bash
# 构建镜像
docker build -t cfiptest:latest .

# 或使用 docker-compose 构建
docker-compose build
```

### 2. 运行容器

#### 交互式模式
```bash
# 使用 Docker 直接运行
docker run -it --rm --network host -v $(pwd)/result:/app/result cfiptest:latest

# 使用 docker-compose 运行
docker-compose run --rm cfiptest
```

#### 命令行模式
```bash
# 更新 IPv4 IP 列表
docker run --rm --network host -v $(pwd)/result:/app/result cfiptest:latest update

# 测试常用地区
docker run --rm --network host -v $(pwd)/result:/app/result cfiptest:latest common

# 自动测试所有地区
docker run --rm --network host -v $(pwd)/result:/app/result cfiptest:latest auto

# 测试指定地区
docker run --rm --network host -v $(pwd)/result:/app/result cfiptest:latest --both AS_HK,AS_SG,AS_JP --maxsc 3 --mins 10

# 只测试 IPv6
docker run --rm --network host -v $(pwd)/result:/app/result cfiptest:latest --ipv6
```

## 📋 使用方式

### 预定义命令

| 命令 | 说明 |
|------|------|
| `interactive` | 交互式模式（默认） |
| `update` | 更新 IPv4 IP 列表 |
| `test` | 测试常用地区（AS_HK,AS_SG,AS_JP） |
| `auto` | 自动测试所有地区 |
| `common` | 测试常用地区 |

### 完整命令行参数

所有原始的命令行参数都支持：

```bash
# 测试指定 IPv4 地区
docker run --rm --network host -v $(pwd)/result:/app/result cfiptest:latest --ipv4 AS_JP,AS_KR

# 同时测试 IPv4 和 IPv6
docker run --rm --network host -v $(pwd)/result:/app/result cfiptest:latest --both AS_JP,AS_KR

# 上传测试结果
docker run --rm --network host -v $(pwd)/result:/app/result cfiptest:latest --upload

# 自定义参数
docker run --rm --network host -v $(pwd)/result:/app/result cfiptest:latest --ipv4 AS_HK --maxsc 5 --mins 15 --output my_result
```

## 🔧 Docker Compose 使用

### 基本使用
```bash
# 启动交互式容器
docker-compose run --rm cfiptest

# 运行特定命令
docker-compose run --rm cfiptest update
docker-compose run --rm cfiptest common
```

### 定时任务
```bash
# 启动定时任务服务（每天凌晨2点自动测试）
docker-compose --profile cron up -d cfiptest-cron

# 查看定时任务日志
docker-compose logs -f cfiptest-cron

# 停止定时任务
docker-compose --profile cron down
```

## 📁 目录挂载

建议挂载以下目录以持久化数据：

- `./result:/app/result` - 测试结果文件
- `./ipv4:/app/ipv4` - IPv4 IP 列表文件
- `./ipv6:/app/ipv6` - IPv6 相关文件

## 🌐 网络配置

### IPv6 支持
为了支持 IPv6 测试，容器需要使用 `--network host` 模式：

```bash
docker run --network host cfiptest:latest
```

### 防火墙注意事项
确保 Docker 主机允许以下网络访问：
- ICMP/ICMPv6 ping 包（用于延迟测试）
- HTTP/HTTPS 访问（用于速度测试和更新）

## 🔍 故障排除

### 1. IPv6 不可用
```bash
# 检查 IPv6 连接
docker run --rm --network host cfiptest:latest --ipv6
```

### 2. 权限问题
```bash
# 确保结果目录有写权限
chmod 755 result/
```

### 3. 网络连接问题
```bash
# 测试网络连接
docker run --rm --network host cfiptest:latest update
```

## 📊 输出文件

测试完成后，结果文件将保存在 `result/` 目录中：

- `ipv4_[地区]_result.csv` - IPv4 测试结果
- `ipv6_result.csv` - IPv6 测试结果
- `final_result.csv` - 合并结果文件
- `[自定义名称].csv` - 自定义输出文件

## 🔧 自定义配置

### 修改默认参数
可以通过环境变量或命令行参数自定义测试配置：

```bash
# 自定义测试次数和速度要求
docker run --rm --network host -v $(pwd)/result:/app/result \
  cfiptest:latest --both AS_HK,AS_SG --maxsc 5 --mins 20
```

### GitHub Token 配置
如果需要上传功能，可以在容器中设置环境变量：

```bash
docker run --rm --network host -v $(pwd)/result:/app/result \
  -e GITHUB_TOKEN=your_token_here \
  cfiptest:latest --upload
```

## 📝 注意事项

1. **网络模式**：建议使用 `--network host` 以获得最佳的网络测试性能
2. **权限**：确保挂载的目录有适当的读写权限
3. **IPv6**：IPv6 测试需要主机系统支持 IPv6
4. **资源**：测试过程可能消耗较多网络带宽和 CPU 资源

## 🆘 获取帮助

```bash
# 查看帮助信息
docker run --rm cfiptest:latest --help

# 查看可用地区代码
docker run --rm cfiptest:latest interactive
```
