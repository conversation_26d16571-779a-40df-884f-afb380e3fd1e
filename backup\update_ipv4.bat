@echo off
setlocal enabledelayedexpansion

title Cloudflare IPv4 IP List Updater

echo ======================================================
echo           Cloudflare IPv4 IP List Updater
echo ======================================================
echo.

rem Check internet connection first
echo Checking internet connection...
ping -n 1 ******* >nul 2>&1
if !errorlevel! neq 0 (
    echo ✗ No internet connection detected. Please check your network.
    echo.
    pause
    exit /b 1
)
echo ✓ Internet connection confirmed
echo.

rem Create ipv4 folder if it doesn't exist
if not exist ipv4 mkdir ipv4

rem Backup existing files if they exist
if exist ipv4\*.txt (
    echo Creating backup of existing files...
    if not exist ipv4\backup mkdir ipv4\backup
    copy ipv4\*.txt ipv4\backup\ >nul 2>&1
    echo ✓ Backup created in ipv4\backup\
    echo.
)

echo Updating IPv4 IP lists from GitHub Gist...
echo Source: https://gist.github.com/xczjzhu/8f10162261172c9937506940d79fb0f8
echo.

rem Base URL for the GitHub Gist
set base_url=https://gist.githubusercontent.com/xczjzhu/8f10162261172c9937506940d79fb0f8/raw

rem Define all regions and their corresponding file names
set regions=AS_HK AS_JP AS_KR AS_SG US_USA EU_WEST EU_CENTRAL EU_EAST OTHER

rem Initialize counters
set success_count=0
set fail_count=0

rem Download each region's IP list
for %%r in (%regions%) do (
    echo [!success_count!/9] Downloading %%r.txt...

    rem Use PowerShell to download the file with timeout and retry
    powershell -Command "$ProgressPreference = 'SilentlyContinue'; try { $response = Invoke-WebRequest -Uri '%base_url%/%%r.txt' -OutFile 'ipv4\%%r.txt' -UseBasicParsing -TimeoutSec 30; Write-Host 'Successfully downloaded %%r.txt' -ForegroundColor Green } catch { Write-Host 'Failed to download %%r.txt: ' $_.Exception.Message -ForegroundColor Red }"

    rem Check if file was downloaded successfully and has content
    if exist ipv4\%%r.txt (
        for /f %%i in ('find /c /v "" ^< ipv4\%%r.txt 2^>nul') do set file_lines=%%i
        if !file_lines! gtr 0 (
            echo ✓ ipv4\%%r.txt updated successfully (!file_lines! IPs)
            set /a success_count+=1
        ) else (
            echo ✗ ipv4\%%r.txt is empty, download may have failed
            set /a fail_count+=1
        )
    ) else (
        echo ✗ Failed to download ipv4\%%r.txt
        set /a fail_count+=1
    )
    echo.
)

echo ======================================================
echo                Update Completed
echo ======================================================
echo.

rem Show download statistics
echo Download Statistics:
echo - Successful: !success_count!/9
echo - Failed: !fail_count!/9
echo.

rem Show summary of downloaded files
echo Summary of IPv4 IP list files:
set total_ips=0
for %%r in (%regions%) do (
    if exist ipv4\%%r.txt (
        for /f %%i in ('find /c /v "" ^< ipv4\%%r.txt 2^>nul') do (
            if %%i gtr 0 (
                echo ✓ %%r.txt: %%i IPs
                set /a total_ips+=%%i
            ) else (
                echo ✗ %%r.txt: Empty file
            )
        )
    ) else (
        echo ✗ %%r.txt: Not available
    )
)

echo.
echo Total IPs downloaded: !total_ips!
echo Files saved in: ipv4\ folder

rem Show completion status
if !fail_count! equ 0 (
    echo.
    echo ✓ All IPv4 IP lists have been successfully updated!
    echo   You can now run cfiptest.bat or cfiptest_auto.bat
) else (
    echo.
    echo ⚠ Some downloads failed. You may want to:
    echo   1. Check your internet connection
    echo   2. Try running this script again
    echo   3. Manually verify the GitHub Gist is accessible
    if exist ipv4\backup (
        echo   4. Restore from backup if needed: ipv4\backup\
    )
)

echo.
echo Current time: %date% %time%
echo.
echo Press any key to exit...
pause >nul
