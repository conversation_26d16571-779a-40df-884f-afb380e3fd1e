#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IPv6 连接测试脚本
用于测试改进后的 IPv6 检测功能
"""

import sys
import os
from pathlib import Path

# 添加 py 目录到路径
sys.path.insert(0, str(Path(__file__).parent / "py"))

from cfiptest import CFIPTester

def main():
    print("======================================================")
    print("               IPv6 连接检测测试")
    print("======================================================")
    print()
    
    # 创建测试器实例
    tester = CFIPTester()
    
    # 执行 IPv6 检测
    ipv6_support = tester.check_ipv6_support()
    
    print()
    print("======================================================")
    print("                  检测结果")
    print("======================================================")
    
    if ipv6_support:
        print("✅ IPv6 连接可用")
        print("   您可以使用以下命令测试 IPv6:")
        print("   python py/cfiptest.py --ipv6")
        print("   python py/cfiptest.py --both AS_HK,AS_SG,AS_JP")
    else:
        print("❌ IPv6 连接不可用")
        print("   建议检查:")
        print("   1. 网络运营商是否支持 IPv6")
        print("   2. 路由器是否启用了 IPv6")
        print("   3. Windows 防火墙是否阻止了 IPv6")
        print("   4. 网络适配器的 IPv6 配置")
    
    print()
    print("======================================================")
    print("              网络配置信息")
    print("======================================================")
    
    # 显示网络配置信息
    try:
        import subprocess
        import platform
        
        if platform.system() == "Windows":
            print("IPv6 配置信息:")
            result = subprocess.run(["ipconfig", "/all"], capture_output=True, text=True, encoding='gbk', errors='ignore')
            lines = result.stdout.split('\n')
            
            # 查找 IPv6 相关信息
            ipv6_found = False
            for line in lines:
                if 'IPv6' in line or '2001:' in line or '2606:' in line or 'fe80:' in line:
                    print(f"  {line.strip()}")
                    ipv6_found = True
            
            if not ipv6_found:
                print("  未找到 IPv6 地址配置")
                
            print()
            print("路由表信息:")
            result = subprocess.run(["route", "print", "-6"], capture_output=True, text=True, encoding='gbk', errors='ignore')
            if result.returncode == 0:
                lines = result.stdout.split('\n')[:10]  # 只显示前10行
                for line in lines:
                    if line.strip():
                        print(f"  {line.strip()}")
            else:
                print("  无法获取 IPv6 路由信息")
                
    except Exception as e:
        print(f"获取网络信息时出错: {e}")
    
    print()
    input("按 Enter 键退出...")

if __name__ == "__main__":
    main()
