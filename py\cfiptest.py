#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cloudflare IP 优选测试工具 - Python版本
支持IPv4/IPv6测试，地区选择，参数配置等功能
"""

import os
import sys
import subprocess
import argparse
import json
import csv
import time
import socket
import requests
from pathlib import Path
from typing import List, Dict, Optional, Tuple
import threading
import platform
import logging

class CFIPTester:
    """Cloudflare IP测试器主类"""

    def __init__(self):
        self.script_dir = Path(__file__).parent.parent
        self.ipv4_dir = self.script_dir / "ipv4"
        self.ipv6_dir = self.script_dir / "ipv6"
        self.result_dir = self.script_dir / "result"
        self.cfiptest_exe = self.script_dir / "cfiptest.exe"

        # 默认参数
        self.default_params = {
            'maxsc': 3,      # 测试次数
            'maxdc': 200,    # 最大延迟(ms)
            'mins': 10,      # 最小速度(MB/s)
            'url': 'https://qiqitest.8699.pp.ua/200m',
            'delay_url': 'qiqitest.8699.pp.ua'
        }

        # 地区配置
        self.regions = {
            'AS_JP': '亚洲-日本',
            'AS_KR': '亚洲-韩国',
            'AS_HK': '亚洲-香港',
            'AS_SG': '亚洲-新加坡',
            'US_USA': '美国',
            'EU_WEST': '欧洲-西部',
            'EU_CENTRAL': '欧洲-中部',
            'EU_EAST': '欧洲-东部',
            'OTHER': '其他地区'
        }

        # 地区代码映射（用于上传格式）
        self.region_codes = {
            'AS_JP': 'JP',
            'AS_KR': 'KR',
            'AS_HK': 'HK',
            'AS_SG': 'SG',
            'US_USA': 'US',
            'EU_WEST': 'EU',
            'EU_CENTRAL': 'EU',
            'EU_EAST': 'EU',
            'OTHER': 'CF'
        }

        # GitHub Gist配置
        self.gist_base_url = "https://gist.githubusercontent.com/xczjzhu/8f10162261172c9937506940d79fb0f8/raw"
        self.github_token = "****************************************"
        self.gist_id = "8f10162261172c9937506940d79fb0f8"

        # 设置日志
        self._setup_logging()

        # 确保目录存在
        self._ensure_directories()

    def _setup_logging(self):
        """设置日志配置"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler(self.script_dir / 'cfiptest.log', encoding='utf-8')
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def _ensure_directories(self):
        """确保必要的目录存在"""
        for directory in [self.ipv4_dir, self.ipv6_dir, self.result_dir]:
            directory.mkdir(exist_ok=True)
    
    def check_ipv6_support(self) -> bool:
        """检测IPv6连接支持"""
        try:
            # 尝试ping Cloudflare的IPv6 DNS
            if platform.system() == "Windows":
                result = subprocess.run(
                    ["ping", "-6", "-n", "1", "2606:4700:4700::1111"],
                    capture_output=True, timeout=10
                )
            else:
                result = subprocess.run(
                    ["ping6", "-c", "1", "2606:4700:4700::1111"],
                    capture_output=True, timeout=10
                )
            return result.returncode == 0
        except:
            return False
    
    def check_internet_connection(self) -> bool:
        """检查网络连接"""
        try:
            if platform.system() == "Windows":
                result = subprocess.run(
                    ["ping", "-n", "1", "*******"],
                    capture_output=True, timeout=10
                )
            else:
                result = subprocess.run(
                    ["ping", "-c", "1", "*******"],
                    capture_output=True, timeout=10
                )
            return result.returncode == 0
        except:
            return False
    
    def update_ipv4_lists(self) -> Dict[str, bool]:
        """更新IPv4 IP列表"""
        print("======================================================")
        print("           Cloudflare IPv4 IP List Updater")
        print("======================================================")
        print()
        
        # 检查网络连接
        print("检查网络连接...")
        if not self.check_internet_connection():
            print("✗ 未检测到网络连接，请检查网络设置")
            return {}
        print("✓ 网络连接正常")
        print()
        
        # 备份现有文件
        backup_dir = self.ipv4_dir / "backup"
        if any(self.ipv4_dir.glob("*.txt")):
            print("创建现有文件备份...")
            backup_dir.mkdir(exist_ok=True)
            for txt_file in self.ipv4_dir.glob("*.txt"):
                if txt_file.name != "backup":
                    backup_path = backup_dir / txt_file.name
                    backup_path.write_bytes(txt_file.read_bytes())
            print("✓ 备份已创建在 ipv4/backup/")
            print()
        
        print("从GitHub Gist更新IPv4 IP列表...")
        print(f"数据源: {self.gist_base_url}")
        print()
        
        results = {}
        success_count = 0
        fail_count = 0
        
        for region in self.regions.keys():
            print(f"[{success_count}/{len(self.regions)}] 下载 {region}.txt...")
            
            try:
                url = f"{self.gist_base_url}/{region}.txt"
                response = requests.get(url, timeout=30)
                response.raise_for_status()
                
                # 保存文件
                file_path = self.ipv4_dir / f"{region}.txt"
                file_path.write_text(response.text, encoding='utf-8')
                
                # 检查文件内容
                lines = response.text.strip().split('\n')
                if lines and lines[0].strip():
                    print(f"✓ ipv4/{region}.txt 更新成功 ({len(lines)} 个IP)")
                    results[region] = True
                    success_count += 1
                else:
                    print(f"✗ ipv4/{region}.txt 文件为空，下载可能失败")
                    results[region] = False
                    fail_count += 1
                    
            except Exception as e:
                print(f"✗ 下载 ipv4/{region}.txt 失败: {str(e)}")
                results[region] = False
                fail_count += 1
            
            print()
        
        # 显示统计信息
        print("======================================================")
        print("                更新完成")
        print("======================================================")
        print()
        print("下载统计:")
        print(f"- 成功: {success_count}/{len(self.regions)}")
        print(f"- 失败: {fail_count}/{len(self.regions)}")
        print()
        
        # 显示文件摘要
        print("IPv4 IP列表文件摘要:")
        total_ips = 0
        for region in self.regions.keys():
            file_path = self.ipv4_dir / f"{region}.txt"
            if file_path.exists():
                try:
                    lines = file_path.read_text(encoding='utf-8').strip().split('\n')
                    ip_count = len([line for line in lines if line.strip()])
                    if ip_count > 0:
                        print(f"✓ {region}.txt: {ip_count} 个IP")
                        total_ips += ip_count
                    else:
                        print(f"✗ {region}.txt: 空文件")
                except:
                    print(f"✗ {region}.txt: 读取失败")
            else:
                print(f"✗ {region}.txt: 文件不存在")
        
        print()
        print(f"总计下载IP数: {total_ips}")
        print(f"文件保存位置: {self.ipv4_dir}")
        
        if fail_count == 0:
            print()
            print("✓ 所有IPv4 IP列表已成功更新!")
            print("  现在可以运行测试脚本")
        else:
            print()
            print("⚠ 部分下载失败，您可以:")
            print("  1. 检查网络连接")
            print("  2. 重新运行更新脚本")
            print("  3. 手动验证GitHub Gist是否可访问")
            if backup_dir.exists():
                print("  4. 如需要可从备份恢复: ipv4/backup/")
        
        return results

    def convert_result_to_upload_format(self, result_file: Path, region_code: str) -> str:
        """将测试结果文件转换为上传格式: ip:端口#地区"""
        try:
            with open(result_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            converted_lines = []
            for i, line in enumerate(lines):
                line = line.strip()
                if not line or i == 0:  # 跳过空行和标题行
                    continue

                # 解析CSV格式的测试结果
                # 格式: IP地址,端口,TLS,数据中心,地区,城市,网络延迟(毫秒),下载速度(MB/s)
                parts = line.split(',')
                if len(parts) < 2:
                    continue

                ip = parts[0].strip()
                port = parts[1].strip()

                # 验证端口是否为数字
                try:
                    int(port)
                except ValueError:
                    continue

                # 检查是否为IPv6地址并添加方括号
                if ':' in ip and ip.count(':') > 1 and not ip.startswith('['):
                    # IPv6地址，添加方括号
                    ip = f"[{ip}]"

                # 格式化输出: ip:端口#地区
                formatted_line = f"{ip}:{port}#{region_code}"
                converted_lines.append(formatted_line)

            return '\n'.join(converted_lines)

        except Exception as e:
            self.logger.error(f"转换结果文件格式时出错 {result_file}: {str(e)}")
            return ""

    def upload_result_to_gist(self, result_file: Path, region_code: str, custom_filename: str = None) -> bool:
        """上传测试结果文件到GitHub Gist（转换为指定格式）"""
        if not self.github_token:
            self.logger.warning("未配置GITHUB_TOKEN，跳过Gist上传")
            return False

        self.logger.info(f"开始上传 {result_file} 到GitHub Gist...")

        try:
            # 转换结果文件格式
            converted_content = self.convert_result_to_upload_format(result_file, region_code)
            if not converted_content:
                print(f"✗ {result_file.name} 转换格式失败或无有效数据，跳过上传")
                return False

            # 生成上传文件名
            if custom_filename:
                upload_filename = custom_filename if custom_filename.endswith('.txt') else f"{custom_filename}.txt"
            else:
                upload_filename = result_file.name.replace('_result.csv', '.txt')

            headers = {
                "Authorization": f"token {self.github_token}",
                "Accept": "application/vnd.github+json"
            }

            data = {
                "files": {
                    upload_filename: {
                        "content": converted_content
                    }
                }
            }

            response = requests.patch(
                f"https://api.github.com/gists/{self.gist_id}",
                headers=headers,
                json=data
            )

            if response.status_code in (200, 201):
                self.logger.info(f"{result_file} 上传到GitHub Gist成功")
                print(f"✓ {upload_filename} 已上传到GitHub Gist (格式: ip:端口#{region_code})")
                return True
            else:
                error_msg = response.json().get('message', '未知错误')
                self.logger.error(f"{result_file} 上传到GitHub Gist失败: {error_msg}")
                print(f"✗ {upload_filename} 上传失败: {error_msg}")
                return False

        except Exception as e:
            self.logger.error(f"{result_file} 上传到GitHub Gist出错: {str(e)}")
            print(f"✗ {result_file.name} 上传出错: {str(e)}")
            return False

    def upload_test_results_to_gist(self) -> Dict[str, bool]:
        """上传测试结果到GitHub Gist"""
        print("======================================================")
        print("           上传测试结果到GitHub Gist")
        print("======================================================")
        print()

        if not self.github_token:
            print("错误: 未配置GitHub Token")
            return {}

        results = {}
        success_count = 0
        fail_count = 0

        # 上传IPv4测试结果
        for region in self.regions.keys():
            result_file = self.result_dir / f"ipv4_{region}_result.csv"
            region_code = self.region_codes.get(region, 'CF')  # 默认使用CF

            if result_file.exists():
                print(f"上传 {region} 测试结果 (地区代码: {region_code})...")
                if self.upload_result_to_gist(result_file, region_code):
                    results[region] = True
                    success_count += 1
                else:
                    results[region] = False
                    fail_count += 1
            else:
                print(f"警告: 测试结果文件 {result_file.name} 不存在，跳过上传")
                results[region] = False
                fail_count += 1

        # 上传IPv6测试结果
        ipv6_result_file = self.result_dir / "ipv6_result.csv"
        if ipv6_result_file.exists():
            print(f"上传 IPv6 测试结果...")
            if self.upload_result_to_gist(ipv6_result_file, 'CF'):
                results['IPv6'] = True
                success_count += 1
            else:
                results['IPv6'] = False
                fail_count += 1
        else:
            print("提示: IPv6测试结果文件不存在，跳过上传")

        print()
        print("======================================================")
        print("                上传完成")
        print("======================================================")
        print()
        print("上传统计:")
        print(f"- 成功: {success_count}")
        print(f"- 失败: {fail_count}")
        print()

        if fail_count == 0:
            print("✓ 所有测试结果已成功上传到GitHub Gist!")
        else:
            print("⚠ 部分文件上传失败")

        return results

    def upload_specific_results_to_gist(self, file_specs: str) -> Dict[str, bool]:
        """上传指定的测试结果到GitHub Gist"""
        print("======================================================")
        print("           上传指定测试结果到GitHub Gist")
        print("======================================================")
        print()

        if not self.github_token:
            print("错误: 未配置GitHub Token")
            return {}

        # 解析文件规格
        if file_specs.lower() == 'all':
            return self.upload_test_results_to_gist()

        file_list = [f.strip() for f in file_specs.split(',') if f.strip()]
        if not file_list:
            print("错误: 未指定有效的文件")
            return {}

        # 检查是否是合并文件上传
        if len(file_list) == 1 and (file_list[0].endswith('.csv') or
                                   (self.result_dir / f"{file_list[0]}.csv").exists()):
            # 这是一个合并文件上传请求
            return {'merged_file': self.upload_merged_result(file_list[0])}

        # 检查是否是合并文件上传
        if len(file_list) == 1 and (file_list[0].endswith('.csv') or
                                   (self.result_dir / f"{file_list[0]}.csv").exists()):
            # 这是一个合并文件上传请求
            return {'merged_file': self.upload_merged_result(file_list[0])}

        results = {}
        success_count = 0
        fail_count = 0

        print(f"将上传以下文件: {', '.join(file_list)}")
        print()

        for file_spec in file_list:
            file_spec = file_spec.upper()

            if file_spec == 'IPV6':
                # 上传IPv6结果
                ipv6_result_file = self.result_dir / "ipv6_result.csv"
                if ipv6_result_file.exists():
                    print(f"上传 IPv6 测试结果...")
                    if self.upload_result_to_gist(ipv6_result_file, 'CF'):
                        results['IPv6'] = True
                        success_count += 1
                    else:
                        results['IPv6'] = False
                        fail_count += 1
                else:
                    print(f"警告: IPv6测试结果文件不存在，跳过上传")
                    results['IPv6'] = False
                    fail_count += 1

            elif file_spec in self.regions:
                # 上传指定地区的IPv4结果
                region = file_spec
                result_file = self.result_dir / f"ipv4_{region}_result.csv"
                region_code = self.region_codes.get(region, 'CF')

                if result_file.exists():
                    print(f"上传 {region} 测试结果 (地区代码: {region_code})...")
                    if self.upload_result_to_gist(result_file, region_code):
                        results[region] = True
                        success_count += 1
                    else:
                        results[region] = False
                        fail_count += 1
                else:
                    print(f"警告: 测试结果文件 {result_file.name} 不存在，跳过上传")
                    results[region] = False
                    fail_count += 1

            else:
                print(f"警告: 无效的文件规格 '{file_spec}'，跳过")
                results[file_spec] = False
                fail_count += 1

        print()
        print("======================================================")
        print("                上传完成")
        print("======================================================")
        print()
        print("上传统计:")
        print(f"- 成功: {success_count}")
        print(f"- 失败: {fail_count}")
        print()

        if fail_count == 0:
            print("✓ 指定的测试结果已成功上传到GitHub Gist!")
        else:
            print("⚠ 部分文件上传失败")

        return results

    def upload_merged_result(self, merged_filename: str, custom_filename: str = None) -> bool:
        """上传合并后的结果文件"""
        print("======================================================")
        print("           上传合并结果文件到GitHub Gist")
        print("======================================================")
        print()

        if not self.github_token:
            print("错误: 未配置GitHub Token")
            return False

        # 确定合并文件路径
        if not merged_filename.endswith('.csv'):
            merged_filename += '.csv'
        merged_file = self.result_dir / merged_filename

        if not merged_file.exists():
            print(f"错误: 合并结果文件 {merged_filename} 不存在")
            print(f"请先运行测试生成合并结果文件")
            return False

        # 确定上传文件名
        if custom_filename:
            upload_filename = custom_filename if custom_filename.endswith('.txt') else f"{custom_filename}.txt"
        else:
            upload_filename = merged_filename.replace('.csv', '.txt')

        print(f"上传合并结果文件: {merged_filename} -> {upload_filename}")

        try:
            # 读取合并文件并转换格式
            with open(merged_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            converted_lines = []
            for i, line in enumerate(lines):
                line = line.strip()
                if not line or i == 0:  # 跳过空行和标题行
                    continue

                # 解析CSV格式: IP,Port,TLS,DC,Region,City,Delay(ms),Speed(MB/s)
                parts = line.split(',')
                if len(parts) < 2:
                    continue

                ip = parts[0].strip()
                port = parts[1].strip()

                # 验证端口是否为数字
                try:
                    int(port)
                except ValueError:
                    continue

                # 根据IP类型确定地区代码
                if ':' in ip and ip.count(':') > 1:
                    # IPv6地址
                    if not ip.startswith('['):
                        ip = f"[{ip}]"
                    region_code = 'CF'  # IPv6统一使用CF
                else:
                    # IPv4地址，根据数据中心或地区信息确定地区代码
                    region_code = 'CF'  # 默认使用CF
                    if len(parts) >= 5:
                        dc = parts[3].strip() if len(parts) > 3 else ''
                        region = parts[4].strip() if len(parts) > 4 else ''

                        # 根据数据中心代码推断地区
                        if 'HKG' in dc or 'Hong Kong' in region:
                            region_code = 'HK'
                        elif 'SIN' in dc or 'Singapore' in region:
                            region_code = 'SG'
                        elif 'NRT' in dc or 'FUK' in dc or 'Japan' in region:
                            region_code = 'JP'
                        elif 'ICN' in dc or 'Korea' in region:
                            region_code = 'KR'
                        elif 'LAX' in dc or 'SJC' in dc or 'United States' in region:
                            region_code = 'US'
                        elif 'Europe' in region:
                            region_code = 'EU'

                # 格式化输出: ip:端口#地区
                formatted_line = f"{ip}:{port}#{region_code}"
                converted_lines.append(formatted_line)

            if not converted_lines:
                print("错误: 合并文件中没有有效的IP数据")
                return False

            converted_content = '\n'.join(converted_lines)

            # 上传到GitHub Gist
            headers = {
                "Authorization": f"token {self.github_token}",
                "Accept": "application/vnd.github+json"
            }

            data = {
                "files": {
                    upload_filename: {
                        "content": converted_content
                    }
                }
            }

            response = requests.patch(
                f"https://api.github.com/gists/{self.gist_id}",
                headers=headers,
                json=data
            )

            if response.status_code in (200, 201):
                self.logger.info(f"{merged_file} 上传到GitHub Gist成功")
                print(f"✓ {upload_filename} 已上传到GitHub Gist ({len(converted_lines)} 个IP)")
                return True
            else:
                error_msg = response.json().get('message', '未知错误')
                self.logger.error(f"{merged_file} 上传到GitHub Gist失败: {error_msg}")
                print(f"✗ {upload_filename} 上传失败: {error_msg}")
                return False

        except Exception as e:
            self.logger.error(f"{merged_file} 上传到GitHub Gist出错: {str(e)}")
            print(f"✗ {upload_filename} 上传出错: {str(e)}")
            return False

    def upload_with_custom_filename(self, file_specs: str, custom_filenames: str = None) -> Dict[str, bool]:
        """使用自定义文件名上传测试结果"""
        print("======================================================")
        print("           上传测试结果到GitHub Gist (自定义文件名)")
        print("======================================================")
        print()

        if not self.github_token:
            print("错误: 未配置GitHub Token")
            return {}

        # 解析文件规格和自定义文件名
        file_list = [f.strip() for f in file_specs.split(',') if f.strip()]
        filename_list = []
        if custom_filenames:
            filename_list = [f.strip() for f in custom_filenames.split(',') if f.strip()]

        if not file_list:
            print("错误: 未指定有效的文件")
            return {}

        # 检查是否是合并文件上传
        if len(file_list) == 1 and (file_list[0].endswith('.csv') or
                                   (self.result_dir / f"{file_list[0]}.csv").exists()):
            # 这是一个合并文件上传请求
            custom_name = filename_list[0] if filename_list else None
            return {'merged_file': self.upload_merged_result(file_list[0], custom_name)}

        results = {}
        success_count = 0
        fail_count = 0

        print(f"将上传以下文件: {', '.join(file_list)}")
        if filename_list:
            print(f"使用自定义文件名: {', '.join(filename_list)}")
        print()

        for i, file_spec in enumerate(file_list):
            file_spec = file_spec.upper()
            custom_name = filename_list[i] if i < len(filename_list) else None

            if file_spec == 'IPV6':
                # 上传IPv6结果
                ipv6_result_file = self.result_dir / "ipv6_result.csv"
                if ipv6_result_file.exists():
                    display_name = custom_name if custom_name else "IPv6"
                    print(f"上传 IPv6 测试结果 -> {display_name}.txt...")
                    if self.upload_result_to_gist(ipv6_result_file, 'CF', custom_name):
                        results['IPv6'] = True
                        success_count += 1
                    else:
                        results['IPv6'] = False
                        fail_count += 1
                else:
                    print(f"警告: IPv6测试结果文件不存在，跳过上传")
                    results['IPv6'] = False
                    fail_count += 1

            elif file_spec in self.regions:
                # 上传指定地区的IPv4结果
                region = file_spec
                result_file = self.result_dir / f"ipv4_{region}_result.csv"
                region_code = self.region_codes.get(region, 'CF')

                if result_file.exists():
                    display_name = custom_name if custom_name else f"ipv4_{region}"
                    print(f"上传 {region} 测试结果 -> {display_name}.txt (地区代码: {region_code})...")
                    if self.upload_result_to_gist(result_file, region_code, custom_name):
                        results[region] = True
                        success_count += 1
                    else:
                        results[region] = False
                        fail_count += 1
                else:
                    print(f"警告: 测试结果文件 {result_file.name} 不存在，跳过上传")
                    results[region] = False
                    fail_count += 1

            else:
                print(f"警告: 无效的文件规格 '{file_spec}'，跳过")
                results[file_spec] = False
                fail_count += 1

        print()
        print("======================================================")
        print("                上传完成")
        print("======================================================")
        print()
        print("上传统计:")
        print(f"- 成功: {success_count}")
        print(f"- 失败: {fail_count}")
        print()

        if fail_count == 0:
            print("✓ 指定的测试结果已成功上传到GitHub Gist!")
        else:
            print("⚠ 部分文件上传失败")

        return results

    def generate_ipv6_addresses(self) -> bool:
        """生成IPv6测试地址"""
        ipv6_cidr_file = self.ipv6_dir / "ips-v6.txt"
        ipv6_generated_file = self.ipv6_dir / "generated_ipv6.txt"
        cf_exe = self.ipv6_dir / "cf.exe"
        
        if not ipv6_cidr_file.exists():
            print(f"警告: IPv6 CIDR文件不存在 ({ipv6_cidr_file})")
            print("提示: 您可以手动添加IPv6 CIDR块到该文件")
            return False
        
        if not cf_exe.exists():
            print(f"错误: cf.exe不存在 ({cf_exe})")
            return False
        
        print("从CIDR块生成IPv6地址...")
        
        try:
            # 切换到ipv6目录并运行cf.exe
            original_dir = os.getcwd()
            os.chdir(self.ipv6_dir)
            
            result = subprocess.run([
                str(cf_exe), "-ips", "6", "-outfile", "generated_ipv6.txt",
                "-random", "true", "-task", "100"
            ], capture_output=True, text=True, timeout=60, encoding='gbk', errors='ignore')
            
            os.chdir(original_dir)
            
            if result.returncode != 0:
                print(f"cf.exe执行失败: {result.stderr}")
                return False
            
            # 处理生成的文件，只保留IP地址
            if ipv6_generated_file.exists():
                print("创建纯IP地址版本...")
                content = ipv6_generated_file.read_text(encoding='utf-8')
                lines = content.split('\n')
                
                # 提取第一列（IP地址）
                ip_lines = []
                for line in lines:
                    if line.strip() and ',' in line:
                        ip = line.split(',')[0].strip()
                        if ip and ip != 'IP地址':
                            ip_lines.append(ip)
                
                if ip_lines:
                    ipv6_generated_file.write_text('\n'.join(ip_lines), encoding='utf-8')
                    print(f"IPv6地址生成成功 ({len(ip_lines)} 个地址)")
                    return True
                else:
                    print("生成的IPv6文件为空")
                    return False
            else:
                print("IPv6地址生成失败")
                return False
                
        except Exception as e:
            print(f"生成IPv6地址时出错: {str(e)}")
            return False

    def run_cfiptest(self, ip_file: Path, output_file: Path, params: Dict) -> bool:
        """运行cfiptest.exe进行IP测试"""
        if not self.cfiptest_exe.exists():
            print(f"错误: cfiptest.exe不存在 ({self.cfiptest_exe})")
            return False

        if not ip_file.exists():
            print(f"错误: IP文件不存在 ({ip_file})")
            return False

        cmd = [
            str(self.cfiptest_exe),
            "-f", str(ip_file),
            "-url", params['url'],
            "-delay_url", params['delay_url'],
            "-maxsc", str(params['maxsc']),
            "-maxdc", str(params['maxdc']),
            "-mins", str(params['mins']),
            "-o", str(output_file)
        ]

        try:
            print(f"执行命令: {' '.join(cmd)}")
            # 使用gbk编码处理Windows中文输出
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300, encoding='gbk', errors='ignore')

            if result.returncode == 0:
                print(f"测试完成，结果保存到: {output_file}")
                return True
            else:
                print(f"cfiptest.exe执行失败: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            print("测试超时")
            return False
        except Exception as e:
            print(f"执行测试时出错: {str(e)}")
            return False

    def test_ipv4_regions(self, regions: List[str], params: Dict) -> Dict[str, bool]:
        """测试IPv4地区"""
        print("===== 开始 IPv4 测试 =====")
        print()

        results = {}
        for region in regions:
            print(f"正在测试地区: {region} ({self.regions.get(region, region)})")
            print()

            ip_file = self.ipv4_dir / f"{region}.txt"
            output_file = self.result_dir / f"ipv4_{region}_result.csv"

            if self.run_cfiptest(ip_file, output_file, params):
                print(f"地区 {region} 测试完成，结果保存到 {output_file}")
                results[region] = True
            else:
                print(f"地区 {region} 测试失败")
                results[region] = False
            print()

        print("IPv4 测试完成")
        return results

    def test_ipv6(self, params: Dict) -> bool:
        """测试IPv6"""
        print("===== 开始 IPv6 测试 =====")
        print()

        # 生成IPv6地址
        if not self.generate_ipv6_addresses():
            return False

        ip_file = self.ipv6_dir / "generated_ipv6.txt"
        output_file = self.result_dir / "ipv6_result.csv"

        print("正在测试IPv6...")
        if self.run_cfiptest(ip_file, output_file, params):
            print(f"IPv6测试完成，结果保存到 {output_file}")
            return True
        else:
            print("IPv6测试失败")
            return False

    def merge_results(self, ipv4_regions: List[str] = None, include_ipv6: bool = False, output_filename: str = None) -> bool:
        """合并测试结果"""
        # 确定输出文件名
        if output_filename:
            if not output_filename.endswith('.csv'):
                output_filename += '.csv'
            final_result_file = self.result_dir / output_filename
        else:
            final_result_file = self.result_dir / "final_result.csv"

        print(f"合并所有结果到文件: {final_result_file.name}...")

        # 创建CSV头部
        header = "IP,Port,TLS,DC,Region,City,Delay(ms),Speed(MB/s)"

        try:
            with open(final_result_file, 'w', encoding='utf-8', newline='') as f:
                f.write(header + '\n')

                # 合并IPv4结果
                if ipv4_regions:
                    for region in ipv4_regions:
                        result_file = self.result_dir / f"ipv4_{region}_result.csv"
                        if result_file.exists():
                            try:
                                with open(result_file, 'r', encoding='utf-8') as rf:
                                    lines = rf.readlines()
                                    # 跳过头部，只添加数据行
                                    for line in lines[1:]:
                                        if line.strip():
                                            f.write(line)
                            except Exception as e:
                                print(f"读取 {result_file} 时出错: {str(e)}")

                # 合并IPv6结果
                if include_ipv6:
                    ipv6_result_file = self.result_dir / "ipv6_result.csv"
                    if ipv6_result_file.exists():
                        try:
                            with open(ipv6_result_file, 'r', encoding='utf-8') as rf:
                                lines = rf.readlines()
                                # 跳过头部，只添加数据行
                                for line in lines[1:]:
                                    if line.strip():
                                        f.write(line)
                        except Exception as e:
                            print(f"读取 {ipv6_result_file} 时出错: {str(e)}")

            print(f"所有结果已合并到 {final_result_file}")
            return True

        except Exception as e:
            print(f"合并结果时出错: {str(e)}")
            return False

    def show_results_summary(self, ipv4_regions: List[str] = None, include_ipv6: bool = False, output_filename: str = None):
        """显示结果摘要"""
        print()
        print("======================================================")
        print("                    测试完成")
        print("======================================================")
        print()
        print("个人结果文件:")

        if ipv4_regions:
            for region in ipv4_regions:
                result_file = self.result_dir / f"ipv4_{region}_result.csv"
                if result_file.exists():
                    print(f"- IPv4 {region}: {result_file}")

        if include_ipv6:
            ipv6_result_file = self.result_dir / "ipv6_result.csv"
            if ipv6_result_file.exists():
                print(f"- IPv6: {ipv6_result_file}")

        print()
        print("合并结果文件:")
        if output_filename:
            if not output_filename.endswith('.csv'):
                output_filename += '.csv'
            final_result_file = self.result_dir / output_filename
        else:
            final_result_file = self.result_dir / "final_result.csv"

        if final_result_file.exists():
            print(f"- {final_result_file}")

        print()

    def interactive_mode(self):
        """交互式模式"""
        # 当前参数
        current_params = self.default_params.copy()

        while True:
            self._show_main_menu(current_params)
            choice = input("请选择操作编号: ").strip()

            if choice == "1":
                self._ipv4_test_menu(current_params)
            elif choice == "2":
                self._ipv6_test_menu(current_params)
            elif choice == "3":
                self._ipv4_ipv6_test_menu(current_params)
            elif choice == "4":
                current_params = self._config_menu(current_params)
            elif choice == "5":
                current_params = self.default_params.copy()
                print()
                print("已恢复默认设置:")
                self._show_current_params(current_params)
                input("按Enter继续...")
            elif choice == "6":
                self.update_ipv4_lists()
                input("按Enter继续...")
            elif choice == "7":
                self._upload_menu()
            elif choice == "8":
                print("退出程序")
                break
            else:
                print("无效选择，请重新选择")
                time.sleep(1)

    def _show_main_menu(self, params: Dict):
        """显示主菜单"""
        os.system('cls' if platform.system() == "Windows" else 'clear')
        print("======================================================")
        print("            Cloudflare IP优选测试工具 - Python版")
        print("======================================================")
        print()
        print("当前设置:")
        self._show_current_params(params)
        print()
        print("请选择操作类型:")
        print()
        print("1. 测试 IPv4（选择地区）")
        print("2. 测试 IPv6")
        print("3. 同时测试 IPv4 和 IPv6")
        print("4. 设置测试参数")
        print("5. 恢复默认设置")
        print("6. 更新IPv4 IP列表")
        print("7. 上传测试结果到GitHub Gist")
        print("8. 退出")
        print()

    def _show_current_params(self, params: Dict):
        """显示当前参数"""
        print(f"- 测试次数: {params['maxsc']}")
        print(f"- 最大延迟: {params['maxdc']} ms")
        print(f"- 最小速度: {params['mins']} MB/s")

    def _ipv4_test_menu(self, params: Dict):
        """IPv4测试菜单"""
        os.system('cls' if platform.system() == "Windows" else 'clear')
        print("======================================================")
        print("               IPv4 测试 - 选择地区")
        print("======================================================")
        print()
        print("请选择要测试的地区（可多选，用逗号分隔）:")
        print()

        region_list = list(self.regions.keys())
        for i, (region, name) in enumerate(self.regions.items(), 1):
            print(f"{i}. {name} ({region})")
        print("0. 全部地区")
        print()

        choice = input("请输入选择的地区编号（如 1,3,5）: ").strip()

        # 解析选择的地区
        selected_regions = []
        try:
            if choice == "0":
                selected_regions = region_list
            else:
                choices = [int(x.strip()) for x in choice.split(',') if x.strip()]
                for c in choices:
                    if 1 <= c <= len(region_list):
                        selected_regions.append(region_list[c-1])
        except ValueError:
            print("输入格式错误")
            input("按Enter继续...")
            return

        if not selected_regions:
            print("未选择任何地区")
            input("按Enter继续...")
            return

        print()
        print("将测试以下地区:", [self.regions[r] for r in selected_regions])
        print("使用当前设置:")
        self._show_current_params(params)
        print()

        # 询问是否自定义输出文件名
        output_filename = input("指定合并结果文件名（直接按Enter使用默认final_result.csv）: ").strip()

        confirm = input("按 Enter 开始测试，输入 back 返回主菜单: ").strip()
        if confirm.lower() == "back":
            return

        # 执行测试
        results = self.test_ipv4_regions(selected_regions, params)
        self.merge_results(ipv4_regions=selected_regions, include_ipv6=False, output_filename=output_filename if output_filename else None)
        self.show_results_summary(ipv4_regions=selected_regions, include_ipv6=False, output_filename=output_filename if output_filename else None)

        input("按Enter继续...")

    def _ipv6_test_menu(self, params: Dict):
        """IPv6测试菜单"""
        os.system('cls' if platform.system() == "Windows" else 'clear')
        print("======================================================")
        print("               IPv6 测试")
        print("======================================================")
        print()
        print("正在检测 IPv6 连接...")

        if self.check_ipv6_support():
            print("检测到 IPv6 连接，开始测试...")
            print("使用当前设置:")
            self._show_current_params(params)
            print()

            confirm = input("按 Enter 开始测试，输入 back 返回主菜单: ").strip()
            if confirm.lower() == "back":
                return

            # 执行测试
            result = self.test_ipv6(params)
            if result:
                self.merge_results(ipv4_regions=None, include_ipv6=True)
                self.show_results_summary(ipv4_regions=None, include_ipv6=True)
        else:
            print("错误：未检测到 IPv6 连接")

        print()
        input("按Enter继续...")

    def _ipv4_ipv6_test_menu(self, params: Dict):
        """IPv4和IPv6同时测试菜单"""
        os.system('cls' if platform.system() == "Windows" else 'clear')
        print("======================================================")
        print("           同时测试 IPv4 和 IPv6")
        print("======================================================")
        print()
        print("正在检测 IPv6 连接...")

        ipv6_support = self.check_ipv6_support()
        if ipv6_support:
            print("检测到 IPv6 连接，将同时测试 IPv4 和 IPv6")
        else:
            print("未检测到 IPv6 连接，将只测试 IPv4")
            time.sleep(2)

        print()
        print("请选择 IPv4 测试地区（可多选，用逗号分隔）:")
        print()

        region_list = list(self.regions.keys())
        for i, (region, name) in enumerate(self.regions.items(), 1):
            print(f"{i}. {name} ({region})")
        print("0. 全部地区")
        print()

        choice = input("请输入选择的地区编号: ").strip()

        # 解析选择的地区
        selected_regions = []
        try:
            if choice == "0":
                selected_regions = region_list
            else:
                choices = [int(x.strip()) for x in choice.split(',') if x.strip()]
                for c in choices:
                    if 1 <= c <= len(region_list):
                        selected_regions.append(region_list[c-1])
        except ValueError:
            print("输入格式错误")
            input("按Enter继续...")
            return

        if not selected_regions:
            print("未选择任何地区")
            input("按Enter继续...")
            return

        print()
        print("将执行测试:")
        print("- IPv4 地区:", [self.regions[r] for r in selected_regions])
        if ipv6_support:
            print("- IPv6")
        print("使用当前设置:")
        self._show_current_params(params)
        print()

        # 询问是否自定义输出文件名
        output_filename = input("指定合并结果文件名（直接按Enter使用默认final_result.csv）: ").strip()

        confirm = input("按 Enter 开始测试，输入 back 返回主菜单: ").strip()
        if confirm.lower() == "back":
            return

        # 执行测试
        ipv4_results = self.test_ipv4_regions(selected_regions, params)
        ipv6_result = False
        if ipv6_support:
            ipv6_result = self.test_ipv6(params)

        self.merge_results(ipv4_regions=selected_regions, include_ipv6=ipv6_result, output_filename=output_filename if output_filename else None)
        self.show_results_summary(ipv4_regions=selected_regions, include_ipv6=ipv6_result, output_filename=output_filename if output_filename else None)

        input("按Enter继续...")

    def _config_menu(self, params: Dict) -> Dict:
        """配置菜单"""
        current_params = params.copy()

        while True:
            os.system('cls' if platform.system() == "Windows" else 'clear')
            print("======================================================")
            print("               设置测试参数")
            print("======================================================")
            print()
            print("当前参数设置:")
            print(f"1. 测试次数: {current_params['maxsc']} (默认: {self.default_params['maxsc']})")
            print(f"2. 最大延迟: {current_params['maxdc']} ms (默认: {self.default_params['maxdc']})")
            print(f"3. 最小速度: {current_params['mins']} MB/s (默认: {self.default_params['mins']})")
            print()
            print("请输入要修改的参数编号，输入 back 返回:")

            choice = input().strip()
            if choice.lower() == "back":
                break

            try:
                if choice == "1":
                    new_value = input(f"请输入新的测试次数（当前: {current_params['maxsc']}, 默认: {self.default_params['maxsc']}）: ")
                    if new_value.strip():
                        current_params['maxsc'] = int(new_value)
                elif choice == "2":
                    new_value = input(f"请输入新的最大延迟（当前: {current_params['maxdc']}, 默认: {self.default_params['maxdc']}）: ")
                    if new_value.strip():
                        current_params['maxdc'] = int(new_value)
                elif choice == "3":
                    new_value = input(f"请输入新的最小速度（当前: {current_params['mins']}, 默认: {self.default_params['mins']}）: ")
                    if new_value.strip():
                        current_params['mins'] = int(new_value)
                else:
                    print("无效选择")
                    time.sleep(1)
            except ValueError:
                print("输入值无效")
                time.sleep(1)

        return current_params

    def _upload_menu(self):
        """上传菜单"""
        os.system('cls' if platform.system() == "Windows" else 'clear')
        print("======================================================")
        print("               上传测试结果到GitHub Gist")
        print("======================================================")
        print()
        print("请选择上传方式:")
        print()
        print("1. 上传所有测试结果")
        print("2. 选择特定地区上传")
        print("3. 只上传IPv6结果")
        print("4. 自定义文件名上传")
        print("5. 返回主菜单")
        print()

        choice = input("请选择操作编号: ").strip()

        if choice == "1":
            print("上传所有测试结果...")
            self.upload_test_results_to_gist()
            input("按Enter继续...")

        elif choice == "2":
            print("可用的地区:")
            region_list = list(self.regions.keys())
            for i, (region, name) in enumerate(self.regions.items(), 1):
                print(f"{i}. {name} ({region})")
            print()

            selection = input("请输入要上传的地区编号（用逗号分隔，如 1,3,5）: ").strip()

            try:
                selected_regions = []
                choices = [int(x.strip()) for x in selection.split(',') if x.strip()]
                for c in choices:
                    if 1 <= c <= len(region_list):
                        selected_regions.append(region_list[c-1])

                if selected_regions:
                    file_specs = ','.join(selected_regions)
                    print(f"上传选定地区: {[self.regions[r] for r in selected_regions]}")
                    self.upload_specific_results_to_gist(file_specs)
                else:
                    print("未选择有效的地区")

            except ValueError:
                print("输入格式错误")

            input("按Enter继续...")

        elif choice == "3":
            print("上传IPv6测试结果...")
            self.upload_specific_results_to_gist("ipv6")
            input("按Enter继续...")

        elif choice == "4":
            print("自定义文件名上传")
            print()
            print("可用的地区:")
            region_list = list(self.regions.keys())
            for i, (region, name) in enumerate(self.regions.items(), 1):
                print(f"{i}. {name} ({region})")
            print(f"{len(region_list)+1}. IPv6")
            print()

            selection = input("请输入要上传的文件编号（用逗号分隔）: ").strip()

            try:
                selected_files = []
                choices = [int(x.strip()) for x in selection.split(',') if x.strip()]
                for c in choices:
                    if 1 <= c <= len(region_list):
                        selected_files.append(region_list[c-1])
                    elif c == len(region_list) + 1:
                        selected_files.append('ipv6')

                if selected_files:
                    print()
                    print("请输入对应的自定义文件名（用逗号分隔，不需要.txt后缀）:")
                    custom_names = input().strip()

                    file_specs = ','.join(selected_files)
                    print(f"上传文件: {selected_files}")
                    if custom_names:
                        print(f"自定义文件名: {custom_names}")
                        self.upload_with_custom_filename(file_specs, custom_names)
                    else:
                        print("未提供自定义文件名，使用默认文件名")
                        self.upload_specific_results_to_gist(file_specs)
                else:
                    print("未选择有效的文件")

            except ValueError:
                print("输入格式错误")

            input("按Enter继续...")

        elif choice == "5":
            return

        else:
            print("无效选择")
            time.sleep(1)
            self._upload_menu()


def create_argument_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="Cloudflare IP优选测试工具 - Python版本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python cfiptest.py                           # 交互式模式
  python cfiptest.py --auto                    # 自动测试所有地区
  python cfiptest.py --ipv4 AS_JP,AS_KR       # 测试指定IPv4地区
  python cfiptest.py --ipv6                    # 只测试IPv6
  python cfiptest.py --both AS_JP,AS_KR       # 同时测试IPv4和IPv6
  python cfiptest.py --update                  # 更新IPv4 IP列表
  python cfiptest.py --upload                  # 上传所有测试结果到GitHub Gist
  python cfiptest.py --upload AS_JP,AS_KR     # 上传指定地区的测试结果
  python cfiptest.py --upload ipv6            # 只上传IPv6测试结果
  python cfiptest.py --upload AS_HK --filename my_hk_ips  # 使用自定义文件名上传
  python cfiptest.py --upload HSJ6 --filename hsj6    # 上传合并结果文件
  python cfiptest.py --ipv4 AS_JP,AS_HK --output jp_hk_result  # 指定合并结果文件名
  python cfiptest.py --common                  # 测试常用地区

地区代码:
  AS_JP (亚洲-日本), AS_KR (亚洲-韩国), AS_HK (亚洲-香港), AS_SG (亚洲-新加坡)
  US_USA (美国), EU_WEST (欧洲-西部), EU_CENTRAL (欧洲-中部), EU_EAST (欧洲-东部)
  OTHER (其他地区)
        """
    )

    # 模式选择（互斥）
    mode_group = parser.add_mutually_exclusive_group()
    mode_group.add_argument('--auto', action='store_true',
                           help='自动测试所有IPv4地区和IPv6（如果支持）')
    mode_group.add_argument('--common', action='store_true',
                           help='测试常用地区（AS_JP, AS_KR, AS_HK, AS_SG）')
    mode_group.add_argument('--ipv4', type=str, metavar='REGIONS',
                           help='测试指定的IPv4地区，用逗号分隔（如：AS_JP,AS_KR）')
    mode_group.add_argument('--ipv6', action='store_true',
                           help='只测试IPv6')
    mode_group.add_argument('--both', type=str, metavar='REGIONS',
                           help='同时测试IPv4和IPv6，指定IPv4地区用逗号分隔')
    mode_group.add_argument('--update', action='store_true',
                           help='更新IPv4 IP列表')
    mode_group.add_argument('--upload', type=str, metavar='FILES', nargs='?', const='all',
                           help='上传测试结果到GitHub Gist，可指定文件（如：AS_JP,AS_KR,ipv6）或使用all上传全部')

    # 测试参数
    parser.add_argument('--maxsc', type=int, default=3, metavar='N',
                       help='测试次数 (默认: 3)')
    parser.add_argument('--maxdc', type=int, default=200, metavar='MS',
                       help='最大延迟毫秒数 (默认: 200)')
    parser.add_argument('--mins', type=int, default=10, metavar='SPEED',
                       help='最小速度 MB/s (默认: 10)')
    parser.add_argument('--url', type=str, default='https://qiqitest.8699.pp.ua/200m',
                       help='测试URL (默认: https://qiqitest.8699.pp.ua/200m)')
    parser.add_argument('--delay-url', type=str, default='qiqitest.8699.pp.ua',
                       help='延迟测试URL (默认: qiqitest.8699.pp.ua)')

    # 其他选项
    parser.add_argument('--no-merge', action='store_true',
                       help='不合并结果文件')
    parser.add_argument('--quiet', '-q', action='store_true',
                       help='静默模式，减少输出')
    parser.add_argument('--filename', type=str, metavar='NAMES',
                       help='指定上传文件名，用逗号分隔（与--upload配合使用）')
    parser.add_argument('--output', type=str, metavar='FILENAME',
                       help='指定合并结果文件名（默认: final_result.csv）')

    return parser


def main():
    """主函数"""
    parser = create_argument_parser()
    args = parser.parse_args()

    # 创建测试器实例
    tester = CFIPTester()

    # 构建测试参数
    test_params = {
        'maxsc': args.maxsc,
        'maxdc': args.maxdc,
        'mins': args.mins,
        'url': args.url,
        'delay_url': args.delay_url
    }

    # 根据参数执行相应操作
    if args.update:
        # 更新IPv4 IP列表
        tester.update_ipv4_lists()

    elif args.upload:
        # 上传测试结果到GitHub Gist
        if args.upload == 'all':
            tester.upload_test_results_to_gist()
        else:
            if args.filename:
                tester.upload_with_custom_filename(args.upload, args.filename)
            else:
                tester.upload_specific_results_to_gist(args.upload)

    elif args.auto:
        # 自动测试所有地区
        print("======================================================")
        print("             Cloudflare IP Test Tool - Auto Version")
        print("======================================================")
        print()

        # 检测IPv6支持
        ipv6_support = tester.check_ipv6_support()
        if ipv6_support:
            print("检测到 IPv6 连接，将测试 IPv4 和 IPv6")
        else:
            print("未检测到 IPv6 连接，将只测试 IPv4")

        print()
        print("开始 IPv4 地区测试...")

        all_regions = list(tester.regions.keys())
        ipv4_results = tester.test_ipv4_regions(all_regions, test_params)

        ipv6_result = False
        if ipv6_support:
            print()
            print("开始 IPv6 测试...")
            ipv6_result = tester.test_ipv6(test_params)

        if not args.no_merge:
            tester.merge_results(ipv4_regions=all_regions, include_ipv6=ipv6_result, output_filename=args.output)

        tester.show_results_summary(ipv4_regions=all_regions, include_ipv6=ipv6_result)

    elif args.common:
        # 测试常用地区
        common_regions = ['AS_JP', 'AS_KR', 'AS_HK', 'AS_SG']

        print("======================================================")
        print("           Cloudflare IP Test Tool - Common Regions")
        print("======================================================")
        print()

        # 检测IPv6支持
        ipv6_support = tester.check_ipv6_support()
        if ipv6_support:
            print("检测到 IPv6 连接，将测试常用 IPv4 地区和 IPv6")
        else:
            print("未检测到 IPv6 连接，将只测试常用 IPv4 地区")

        print()
        print("开始常用地区测试...")

        ipv4_results = tester.test_ipv4_regions(common_regions, test_params)

        ipv6_result = False
        if ipv6_support:
            print()
            print("开始 IPv6 测试...")
            ipv6_result = tester.test_ipv6(test_params)

        if not args.no_merge:
            tester.merge_results(ipv4_regions=common_regions, include_ipv6=ipv6_result, output_filename=args.output)

        tester.show_results_summary(ipv4_regions=common_regions, include_ipv6=ipv6_result)

    elif args.ipv4:
        # 测试指定IPv4地区
        regions = [r.strip() for r in args.ipv4.split(',') if r.strip()]

        # 验证地区代码
        valid_regions = []
        for region in regions:
            if region in tester.regions:
                valid_regions.append(region)
            else:
                print(f"警告: 无效的地区代码 '{region}'，已忽略")

        if not valid_regions:
            print("错误: 没有有效的地区代码")
            return 1

        print(f"测试 IPv4 地区: {[tester.regions[r] for r in valid_regions]}")

        ipv4_results = tester.test_ipv4_regions(valid_regions, test_params)

        if not args.no_merge:
            tester.merge_results(ipv4_regions=valid_regions, include_ipv6=False, output_filename=args.output)

        tester.show_results_summary(ipv4_regions=valid_regions, include_ipv6=False)

    elif args.ipv6:
        # 只测试IPv6
        print("测试 IPv6...")

        if not tester.check_ipv6_support():
            print("错误: 未检测到 IPv6 连接")
            return 1

        ipv6_result = tester.test_ipv6(test_params)

        if not args.no_merge:
            tester.merge_results(ipv4_regions=None, include_ipv6=ipv6_result, output_filename=args.output)

        tester.show_results_summary(ipv4_regions=None, include_ipv6=ipv6_result)

    elif args.both:
        # 同时测试IPv4和IPv6
        regions = [r.strip() for r in args.both.split(',') if r.strip()]

        # 验证地区代码
        valid_regions = []
        for region in regions:
            if region in tester.regions:
                valid_regions.append(region)
            else:
                print(f"警告: 无效的地区代码 '{region}'，已忽略")

        if not valid_regions:
            print("错误: 没有有效的地区代码")
            return 1

        print(f"同时测试 IPv4 地区: {[tester.regions[r] for r in valid_regions]} 和 IPv6")

        if not tester.check_ipv6_support():
            print("警告: 未检测到 IPv6 连接，将只测试 IPv4")
            ipv6_support = False
        else:
            ipv6_support = True

        ipv4_results = tester.test_ipv4_regions(valid_regions, test_params)

        ipv6_result = False
        if ipv6_support:
            ipv6_result = tester.test_ipv6(test_params)

        if not args.no_merge:
            tester.merge_results(ipv4_regions=valid_regions, include_ipv6=ipv6_result, output_filename=args.output)

        tester.show_results_summary(ipv4_regions=valid_regions, include_ipv6=ipv6_result)

    else:
        # 默认进入交互式模式
        tester.interactive_mode()

    return 0


if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n程序执行出错: {str(e)}")
        sys.exit(1)
