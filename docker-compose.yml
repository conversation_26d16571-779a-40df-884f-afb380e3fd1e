version: '3.8'

services:
  cfiptest:
    build: .
    image: cfiptest:latest
    container_name: cfiptest
    # 网络模式设置为 host 以支持 IPv6 和网络测试
    network_mode: host
    # 挂载卷以持久化结果
    volumes:
      - ./result:/app/result
      - ./ipv4:/app/ipv4
      - ./ipv6:/app/ipv6
    # 环境变量
    environment:
      - PYTHONUNBUFFERED=1
    # 默认命令
    command: ["interactive"]
    # 如果需要交互式终端
    stdin_open: true
    tty: true

  # 定时任务服务（可选）
  cfiptest-cron:
    build: .
    image: cfiptest:latest
    container_name: cfiptest-cron
    network_mode: host
    volumes:
      - ./result:/app/result
      - ./ipv4:/app/ipv4
      - ./ipv6:/app/ipv6
    environment:
      - PYTHONUNBUFFERED=1
    # 每天凌晨2点执行测试
    command: >
      sh -c "
        while true; do
          echo 'Starting scheduled IP test...'
          python py/cfiptest.py --update
          python py/cfiptest.py --common --output daily_result
          echo 'Scheduled test completed. Sleeping for 24 hours...'
          sleep 86400
        done
      "
    restart: unless-stopped
    profiles:
      - cron
