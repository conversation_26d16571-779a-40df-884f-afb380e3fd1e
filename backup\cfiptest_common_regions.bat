@echo off
setlocal enabledelayedexpansion

title Cloudflare IP Test Tool - Auto Version

echo ======================================================
echo             Cloudflare IP Test Tool - Auto Version
echo ======================================================
echo.

rem Create result folder if it doesn't exist
if not exist result mkdir result
echo Result files will be saved to the result folder.

echo Detecting IPv6 connection...
ping -6 -n 1 2606:4700:4700::1111 >nul 2>&1
if !errorlevel! equ 0 (
    set ipv6_support=1
    echo IPv6 connection detected, will test both IPv4 and IPv6
) else (
    set ipv6_support=0
    echo No IPv6 connection detected, will only test IPv4
)

set maxsc=3
set maxdc=200
set mins=10

echo.
echo Starting IPv4 tests by region...
echo.

set regions=AS_JP AS_KR AS_HK AS_SG US_USA EU_WEST EU_CENTRAL EU_EAST OTHER

for %%r in (%regions%) do (
    echo.
    echo Testing region: %%r
    echo.

    if exist ipv4\%%r.txt (
        .\cfiptest.exe -f ipv4\%%r.txt -url https://qiqitest.8699.pp.ua/200m -delay_url qiqitest.8699.pp.ua -maxsc !maxsc! -maxdc !maxdc! -mins !mins! -o result\ipv4_%%r_result.csv
        echo.
        echo Region %%r test completed, results saved to result\ipv4_%%r_result.csv
    ) else (
        echo Warning: File ipv4\%%r.txt not found, skipping this region.
    )
)

echo.
echo All IPv4 regions tested.

if !ipv6_support! equ 1 (
    echo.
    echo Starting IPv6 test...
    echo.

    set ipv6_cidr_file=ipv6\ips-v6.txt
    set ipv6_generated_file=ipv6\generated_ipv6.txt

    if exist !ipv6_cidr_file! (
        echo Generating IPv6 addresses from CIDR blocks...

        rem Switch to ipv6 directory
        pushd ipv6

        rem Generate IPv6 addresses using cf.exe
        .\cf.exe -ips 6 -outfile generated_ipv6.txt -random true -task 100

        rem Process the generated file to extract only IP addresses
        echo Creating IP-only version of the generated file...

        rem Create a temporary file with only the IP addresses (first column)
        if exist generated_ipv6.txt (
            rem Create a new file with only IP addresses
            powershell -Command "Get-Content generated_ipv6.txt | ForEach-Object { if ($_ -match '^([^,]+),') { $matches[1] } } | Where-Object { $_ -ne 'IP???' } | Set-Content -Path generated_ipv6_clean.txt"

            rem Replace the original file with the clean version
            del generated_ipv6.txt
            rename generated_ipv6_clean.txt generated_ipv6.txt
        )

        rem Return to original directory
        popd

        if exist !ipv6_generated_file! (
            echo IPv6 addresses generated successfully.
            echo.
            echo Testing IPv6 addresses...

            .\cfiptest.exe -f !ipv6_generated_file! -url https://qiqitest.8699.pp.ua/200m -delay_url qiqitest.8699.pp.ua -maxsc !maxsc! -maxdc !maxdc! -mins !mins! -o result\ipv6_result.csv
            echo.
            echo IPv6 test completed, results saved to result\ipv6_result.csv
        ) else (
            echo Error: Failed to generate IPv6 addresses.
        )
    ) else (
        echo Warning: IPv6 CIDR file not found. Please make sure ipv6\ips-v6.txt exists.

        if not exist ipv6 mkdir ipv6

        echo Tip: You can manually add IPv6 CIDR blocks to ipv6\ips-v6.txt file.
    )
)

echo.
echo Merging all results into a single file...

rem Create header line for the summary file
echo IP,Port,TLS,DC,Region,City,Delay(ms),Speed(MB/s) > result\final_result.csv

rem Merge all IPv4 results
for %%r in (%regions%) do (
    if exist result\ipv4_%%r_result.csv (
        rem Skip header line, only add data lines
        for /f "skip=1 delims=" %%a in (result\ipv4_%%r_result.csv) do (
            echo %%a >> result\final_result.csv
        )
    )
)

rem Merge IPv6 results if available
if !ipv6_support! equ 1 (
    if exist result\ipv6_result.csv (
        rem Skip header line, only add data lines
        for /f "skip=1 delims=" %%a in (result\ipv6_result.csv) do (
            echo %%a >> result\final_result.csv
        )
    )
)

rem Results have been merged (no sorting)
if exist result\final_result.csv (
    echo All results have been merged into result\final_result.csv
)

echo.
echo ======================================================
echo                    Test Completed
echo ======================================================
echo.
echo Individual result files:
for %%r in (%regions%) do (
    if exist result\ipv4_%%r_result.csv echo - IPv4 %%r: result\ipv4_%%r_result.csv
)
if !ipv6_support! equ 1 (
    if exist result\ipv6_result.csv echo - IPv6: result\ipv6_result.csv
)
echo.
echo Combined result file:
echo - result\final_result.csv
echo.
echo Script will close in 10 seconds...
timeout /t 10 >nul
