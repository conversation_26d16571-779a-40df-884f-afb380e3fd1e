# 使用官方 Python 3.12 镜像作为基础镜像
FROM python:3.12-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    iputils-ping \
    wget \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 复制 requirements.txt 并安装 Python 依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制项目文件
COPY py/ ./py/
COPY ipv4/ ./ipv4/
COPY ipv6/ ./ipv6/
COPY cfiptest.exe ./
COPY run_test.bat ./

# 创建必要的目录
RUN mkdir -p result

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# 创建启动脚本
RUN echo '#!/bin/bash\n\
# 检查是否为交互式模式\n\
if [ "$1" = "interactive" ]; then\n\
    python py/cfiptest.py\n\
elif [ "$1" = "update" ]; then\n\
    python py/cfiptest.py --update\n\
elif [ "$1" = "test" ]; then\n\
    # 默认测试常用地区\n\
    python py/cfiptest.py --both AS_HK,AS_SG,AS_JP --maxsc 3 --mins 10 --output result\n\
elif [ "$1" = "auto" ]; then\n\
    python py/cfiptest.py --auto\n\
elif [ "$1" = "common" ]; then\n\
    python py/cfiptest.py --common\n\
else\n\
    # 传递所有参数给 Python 脚本\n\
    python py/cfiptest.py "$@"\n\
fi' > /app/entrypoint.sh && chmod +x /app/entrypoint.sh

# 暴露端口（如果需要的话）
# EXPOSE 8080

# 设置默认命令
ENTRYPOINT ["/app/entrypoint.sh"]
CMD ["interactive"]
